[package]
name = "mistersmith"
version = "0.1.0"
edition = "2021"
authors = ["Mister<PERSON><PERSON> Contributors"]
description = "Multi-agent orchestration framework for distributed AI systems"
license = "MIT"
repository = "https://github.com/MattMagg/<PERSON><PERSON>"
keywords = ["async", "agents", "distributed", "orchestration", "ai"]
categories = ["asynchronous", "network-programming"]

[dependencies]
# Core async runtime
tokio = { version = "1.45.0", features = ["full"] }
tokio-util = "0.7"

# Messaging and transport
async-nats = "0.37.0"
uuid = { version = "1.11.0", features = ["v4", "serde"] }
futures = "0.3"

# Serialization
serde = { version = "1.0.214", features = ["derive"] }
serde_json = "1.0"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = "0.3"

# Configuration
config = "0.14"

# Metrics and monitoring
prometheus = "0.13"
hyper = { version = "0.14", features = ["server", "http1", "http2", "runtime"] }
opentelemetry = "0.29"
opentelemetry-prometheus = "0.29"
opentelemetry_sdk = "0.29"

# System information
sysinfo = "0.31"

# Async utilities
async-trait = "0.1"

# Random number generation
fastrand = "2.0"

[dev-dependencies]
tokio-test = "0.4"
criterion = { version = "0.5", features = ["html_reports"] }

[[bin]]
name = "mistersmith"
path = "src/main.rs"

[[bench]]
name = "performance_baseline"
harness = false