{"rustc": 12610991425282158916, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 12903499937715301965, "deps": [[1017461770342116999, "sharded_slab", false, 7993340232391647849], [1359731229228270592, "thread_local", false, 1777203904015402216], [3424551429995674438, "tracing_core", false, 2012655721689025659], [3666196340704888985, "smallvec", false, 7939581193246873051], [8614575489689151157, "nu_ansi_term", false, 2384737491052252719], [10806489435541507125, "tracing_log", false, 8156272320355211837]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-1ea55e85670b2d9f/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}