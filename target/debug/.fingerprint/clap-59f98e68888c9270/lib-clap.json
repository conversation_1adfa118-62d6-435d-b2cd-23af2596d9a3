{"rustc": 12610991425282158916, "features": "[\"std\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9589772125425470163, "path": 6249574996890450387, "deps": [[14814905555676593471, "clap_builder", false, 11525180297092102639]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-59f98e68888c9270/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}