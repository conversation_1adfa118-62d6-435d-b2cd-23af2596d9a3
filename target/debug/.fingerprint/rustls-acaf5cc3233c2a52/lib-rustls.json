{"rustc": 12610991425282158916, "features": "[\"ring\", \"std\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 15030315195695952907, "path": 11283821736962383835, "deps": [[2883436298747778685, "pki_types", false, 16823695819249288091], [3722963349756955755, "once_cell", false, 4091844186071133506], [5491919304041016563, "ring", false, 9124112448566647673], [6528079939221783635, "zeroize", false, 3396793980078304282], [16400140949089969347, "build_script_build", false, 6780360195398507089], [17003143334332120809, "subtle", false, 9857455237757922917], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 2934633037619079206]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-acaf5cc3233c2a52/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}