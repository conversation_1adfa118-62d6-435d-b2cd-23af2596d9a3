{"rustc": 12610991425282158916, "features": "[\"default\", \"internal-logs\", \"tracing\"]", "declared_features": "[\"default\", \"internal-logs\", \"prometheus-encoding\", \"tracing\"]", "target": 10464038996761535284, "profile": 5347358027863023418, "path": 4765660005912635784, "deps": [[3722963349756955755, "once_cell", false, 7535494358102297920], [8015895921862041852, "prometheus", false, 11524766546244446474], [8606274917505247608, "tracing", false, 3846786392553175704], [12256324113281606461, "opentelemetry_sdk", false, 3778095043006908360], [14672055677138825049, "opentelemetry", false, 9383980944533625639]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/opentelemetry-prometheus-292b8c2efdee3a30/dep-lib-opentelemetry_prometheus", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}