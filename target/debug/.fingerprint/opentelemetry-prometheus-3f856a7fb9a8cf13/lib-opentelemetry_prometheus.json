{"rustc": 12610991425282158916, "features": "[\"default\", \"internal-logs\", \"tracing\"]", "declared_features": "[\"default\", \"internal-logs\", \"prometheus-encoding\", \"tracing\"]", "target": 10464038996761535284, "profile": 8276155916380437441, "path": 4765660005912635784, "deps": [[3722963349756955755, "once_cell", false, 4091844186071133506], [8015895921862041852, "prometheus", false, 7393914515867410423], [8606274917505247608, "tracing", false, 3419467303381592544], [12256324113281606461, "opentelemetry_sdk", false, 9759238899136431411], [14672055677138825049, "opentelemetry", false, 2053783602460223099]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/opentelemetry-prometheus-3f856a7fb9a8cf13/dep-lib-opentelemetry_prometheus", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}