{"rustc": 12610991425282158916, "features": "[\"default\", \"futures\", \"futures-core\", \"futures-sink\", \"internal-logs\", \"logs\", \"metrics\", \"pin-project-lite\", \"thiserror\", \"trace\", \"tracing\"]", "declared_features": "[\"default\", \"futures\", \"futures-core\", \"futures-sink\", \"internal-logs\", \"logs\", \"metrics\", \"pin-project-lite\", \"spec_unstable_logs_enabled\", \"testing\", \"thiserror\", \"trace\", \"tracing\"]", "target": 739713175091296742, "profile": 7796973068246518156, "path": 1405446764223603845, "deps": [[1906322745568073236, "pin_project_lite", false, 7015933763304356685], [7013762810557009322, "futures_sink", false, 9963224280444618544], [7620660491849607393, "futures_core", false, 1499607336199572240], [8606274917505247608, "tracing", false, 3846786392553175704], [10806645703491011684, "thiserror", false, 8945616088228759786]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/opentelemetry-c35a01cb8947bcb0/dep-lib-opentelemetry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}