/Users/<USER>/<PERSON>-<PERSON>/MisterSmith/target/debug/deps/mistersmith-17baa5db7274357a.d: src/lib.rs src/agent/mod.rs src/agent/agent.rs src/agent/lifecycle.rs src/agent/state.rs src/agent/pool.rs src/runtime/mod.rs src/runtime/process.rs src/runtime/supervisor.rs src/runtime/command.rs src/runtime/claude_executor.rs src/message/mod.rs src/message/types.rs src/message/router.rs src/message/handler.rs src/transport/mod.rs src/transport/nats.rs src/transport/connection.rs src/supervision/mod.rs src/supervision/types.rs src/supervision/tree.rs src/supervision/node.rs src/supervision/failure_detector.rs src/supervision/supervisors.rs src/supervision/circuit_breaker.rs src/supervision/test_compile.rs src/metrics/mod.rs src/metrics/agent_metrics.rs src/metrics/baselines.rs src/metrics/collector.rs src/metrics/exporter.rs src/metrics/system_metrics.rs src/metrics/types.rs src/tests/mod.rs src/tests/agent/mod.rs src/tests/runtime/mod.rs src/tests/message/mod.rs src/tests/transport/mod.rs src/tests/integration/mod.rs src/tests/integration/claude_cli.rs src/tests/integration/agent_lifecycle.rs src/tests/integration/concurrent_agents.rs src/tests/integration/error_scenarios.rs src/tests/integration/claude_executor_test.rs src/tests/performance/mod.rs src/tests/performance/agent_performance.rs src/tests/performance/message_throughput.rs src/tests/performance/system_load.rs src/tests/supervision/mod.rs src/tests/supervision/tree_tests.rs src/tests/supervision/strategy_tests.rs src/tests/supervision/failure_detection_tests.rs src/tests/supervision/circuit_breaker_tests.rs src/tests/supervision/integration_tests.rs src/tests/claude_code_integration_test.rs

/Users/<USER>/Mister-Smith/MisterSmith/target/debug/deps/mistersmith-17baa5db7274357a: src/lib.rs src/agent/mod.rs src/agent/agent.rs src/agent/lifecycle.rs src/agent/state.rs src/agent/pool.rs src/runtime/mod.rs src/runtime/process.rs src/runtime/supervisor.rs src/runtime/command.rs src/runtime/claude_executor.rs src/message/mod.rs src/message/types.rs src/message/router.rs src/message/handler.rs src/transport/mod.rs src/transport/nats.rs src/transport/connection.rs src/supervision/mod.rs src/supervision/types.rs src/supervision/tree.rs src/supervision/node.rs src/supervision/failure_detector.rs src/supervision/supervisors.rs src/supervision/circuit_breaker.rs src/supervision/test_compile.rs src/metrics/mod.rs src/metrics/agent_metrics.rs src/metrics/baselines.rs src/metrics/collector.rs src/metrics/exporter.rs src/metrics/system_metrics.rs src/metrics/types.rs src/tests/mod.rs src/tests/agent/mod.rs src/tests/runtime/mod.rs src/tests/message/mod.rs src/tests/transport/mod.rs src/tests/integration/mod.rs src/tests/integration/claude_cli.rs src/tests/integration/agent_lifecycle.rs src/tests/integration/concurrent_agents.rs src/tests/integration/error_scenarios.rs src/tests/integration/claude_executor_test.rs src/tests/performance/mod.rs src/tests/performance/agent_performance.rs src/tests/performance/message_throughput.rs src/tests/performance/system_load.rs src/tests/supervision/mod.rs src/tests/supervision/tree_tests.rs src/tests/supervision/strategy_tests.rs src/tests/supervision/failure_detection_tests.rs src/tests/supervision/circuit_breaker_tests.rs src/tests/supervision/integration_tests.rs src/tests/claude_code_integration_test.rs

src/lib.rs:
src/agent/mod.rs:
src/agent/agent.rs:
src/agent/lifecycle.rs:
src/agent/state.rs:
src/agent/pool.rs:
src/runtime/mod.rs:
src/runtime/process.rs:
src/runtime/supervisor.rs:
src/runtime/command.rs:
src/runtime/claude_executor.rs:
src/message/mod.rs:
src/message/types.rs:
src/message/router.rs:
src/message/handler.rs:
src/transport/mod.rs:
src/transport/nats.rs:
src/transport/connection.rs:
src/supervision/mod.rs:
src/supervision/types.rs:
src/supervision/tree.rs:
src/supervision/node.rs:
src/supervision/failure_detector.rs:
src/supervision/supervisors.rs:
src/supervision/circuit_breaker.rs:
src/supervision/test_compile.rs:
src/metrics/mod.rs:
src/metrics/agent_metrics.rs:
src/metrics/baselines.rs:
src/metrics/collector.rs:
src/metrics/exporter.rs:
src/metrics/system_metrics.rs:
src/metrics/types.rs:
src/tests/mod.rs:
src/tests/agent/mod.rs:
src/tests/runtime/mod.rs:
src/tests/message/mod.rs:
src/tests/transport/mod.rs:
src/tests/integration/mod.rs:
src/tests/integration/claude_cli.rs:
src/tests/integration/agent_lifecycle.rs:
src/tests/integration/concurrent_agents.rs:
src/tests/integration/error_scenarios.rs:
src/tests/integration/claude_executor_test.rs:
src/tests/performance/mod.rs:
src/tests/performance/agent_performance.rs:
src/tests/performance/message_throughput.rs:
src/tests/performance/system_load.rs:
src/tests/supervision/mod.rs:
src/tests/supervision/tree_tests.rs:
src/tests/supervision/strategy_tests.rs:
src/tests/supervision/failure_detection_tests.rs:
src/tests/supervision/circuit_breaker_tests.rs:
src/tests/supervision/integration_tests.rs:
src/tests/claude_code_integration_test.rs:

# env-dep:CARGO_PKG_VERSION=0.1.0
